package com.moyu.chuanqirensheng.sub.config

import com.moyu.core.config.ALLY_FILE_NAME
import com.moyu.core.config.ALLY_LEVEL_FILE_NAME
import com.moyu.core.config.ALLY_STAR_FILE_NAME
import com.moyu.core.config.ARENA_FILE_NAME
import com.moyu.core.config.AllyConfigParser
import com.moyu.core.config.AllyLevelConfigParser
import com.moyu.core.config.AllyStarConfigParser
import com.moyu.core.config.ArenaConfigParser
import com.moyu.core.config.BUFF_FILE_NAME
import com.moyu.core.config.BattlePassConfigParser
import com.moyu.core.config.BuffConfigParser
import com.moyu.core.config.COMBINEDBUFF_FILE_NAME
import com.moyu.core.config.COMMON_FILE_NAME
import com.moyu.core.config.CommonConfigParser
import com.moyu.core.config.ConfigHolder
import com.moyu.core.config.DAY_REWARD_FILE_NAME
import com.moyu.core.config.DRAW_FILE_NAME
import com.moyu.core.config.DUNGEON_FILE_NAME
import com.moyu.core.config.DayRewardConfigParser
import com.moyu.core.config.DrawItemConfigParser
import com.moyu.core.config.DungeonConfigParser
import com.moyu.core.config.EQUIPMENT_FILE_NAME
import com.moyu.core.config.EVENT_FILE_NAME
import com.moyu.core.config.EquipmentConfigParser
import com.moyu.core.config.EventConfigParser
import com.moyu.core.config.GIFT_FILE_NAME
import com.moyu.core.config.GiftConfigParser
import com.moyu.core.config.LUCKY_FILE_NAME
import com.moyu.core.config.LuckyConfigParser
import com.moyu.core.config.POOL_FILE_NAME
import com.moyu.core.config.POSITION_FILE_NAME
import com.moyu.core.config.PVP_FILE_NAME
import com.moyu.core.config.PVP_RANK_FILE_NAME
import com.moyu.core.config.PoolConfigParser
import com.moyu.core.config.PvpConfigParser
import com.moyu.core.config.PvpRankConfigParser
import com.moyu.core.config.QuestConfigParser
import com.moyu.core.config.REPUTATION_LEVEL_FILE_NAME
import com.moyu.core.config.ReputationLevelConfigParser
import com.moyu.core.config.SCROLL_FILE_NAME
import com.moyu.core.config.SELL_FILE_NAME
import com.moyu.core.config.SIGN_FILE_NAME
import com.moyu.core.config.SKILL_FILE_NAME
import com.moyu.core.config.STORY_FILE_NAME
import com.moyu.core.config.ScrollConfigParser
import com.moyu.core.config.SellConfigParser
import com.moyu.core.config.SignConfigParser
import com.moyu.core.config.SkillConfigParser
import com.moyu.core.config.StoryConfigParser
import com.moyu.core.config.TALENT1_FILE_NAME
import com.moyu.core.config.TALENT2_FILE_NAME
import com.moyu.core.config.TALENT3_FILE_NAME
import com.moyu.core.config.TASK_FILE_NAME
import com.moyu.core.config.TCG_FILE_NAME
import com.moyu.core.config.TOWER_FILE_NAME
import com.moyu.core.config.TURNTABLE_FILE_NAME
import com.moyu.core.config.TalentConfigParser
import com.moyu.core.config.TcgConfigParser
import com.moyu.core.config.TitleConfigParser
import com.moyu.core.config.TowerConfigParser
import com.moyu.core.config.TurnTableConfigParser
import com.moyu.core.config.UNLOCK_FILE_NAME
import com.moyu.core.config.UnlockConfigParser
import com.moyu.core.config.VIP_FILE_NAME
import com.moyu.core.config.VipConfigParser
import com.moyu.core.config.WAR_PASS1_FILE_NAME
import com.moyu.core.config.WAR_PASS2_FILE_NAME
import com.moyu.core.config.WAR_PASS3_FILE_NAME
import com.moyu.core.config.WAR_PASS4_FILE_NAME

object ConfigManager {
    val configLoaders = listOf(
        ConfigLoader(BUFF_FILE_NAME, BuffConfigParser()),
        ConfigLoader(SKILL_FILE_NAME, SkillConfigParser()),
        ConfigLoader(EQUIPMENT_FILE_NAME, EquipmentConfigParser()),
        ConfigLoader(COMMON_FILE_NAME, CommonConfigParser()),
        ConfigLoader(TALENT1_FILE_NAME, TalentConfigParser()),
        ConfigLoader(TALENT2_FILE_NAME, TalentConfigParser()),
        ConfigLoader(TALENT3_FILE_NAME, TalentConfigParser()),
        ConfigLoader(SELL_FILE_NAME, SellConfigParser()),
        ConfigLoader(TASK_FILE_NAME, QuestConfigParser()),
        ConfigLoader(UNLOCK_FILE_NAME, UnlockConfigParser()),
        ConfigLoader(SCROLL_FILE_NAME, ScrollConfigParser()),
        ConfigLoader(ALLY_FILE_NAME, AllyConfigParser()),
        ConfigLoader(DRAW_FILE_NAME, DrawItemConfigParser()),
        ConfigLoader(GIFT_FILE_NAME, GiftConfigParser()),
        ConfigLoader(EVENT_FILE_NAME, EventConfigParser()),
        ConfigLoader(POOL_FILE_NAME, PoolConfigParser()),
        ConfigLoader(SIGN_FILE_NAME, SignConfigParser()),
        ConfigLoader(VIP_FILE_NAME, VipConfigParser()),
        ConfigLoader(ARENA_FILE_NAME, ArenaConfigParser()),
        ConfigLoader(COMBINEDBUFF_FILE_NAME, BuffConfigParser()),
        ConfigLoader(WAR_PASS1_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(WAR_PASS2_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(WAR_PASS3_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(WAR_PASS4_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(POSITION_FILE_NAME, TitleConfigParser()),
        ConfigLoader(PVP_FILE_NAME, PvpConfigParser()),
        ConfigLoader(DAY_REWARD_FILE_NAME, DayRewardConfigParser()),
        ConfigLoader(TURNTABLE_FILE_NAME, TurnTableConfigParser()),
        ConfigLoader(TOWER_FILE_NAME, TowerConfigParser()),
        ConfigLoader(DUNGEON_FILE_NAME, DungeonConfigParser()),
        ConfigLoader(STORY_FILE_NAME, StoryConfigParser()),
        ConfigLoader(REPUTATION_LEVEL_FILE_NAME, ReputationLevelConfigParser()),
        ConfigLoader(ALLY_LEVEL_FILE_NAME, AllyLevelConfigParser()),
        ConfigLoader(ALLY_STAR_FILE_NAME, AllyStarConfigParser()),
        ConfigLoader(LUCKY_FILE_NAME, LuckyConfigParser()),
        ConfigLoader(PVP_RANK_FILE_NAME, PvpRankConfigParser()),
        ConfigLoader(TCG_FILE_NAME, TcgConfigParser()),
    )

    suspend fun loadConfigs(configHolder: ConfigHolder) {
        configLoaders.forEach {
            it.loadConfig()
            configHolder.setGameConfig(it.getKey(), it.getConfig())
        }
    }
}