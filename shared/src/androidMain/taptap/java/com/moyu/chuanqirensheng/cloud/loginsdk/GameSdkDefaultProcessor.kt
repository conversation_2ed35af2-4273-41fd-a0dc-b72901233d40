package com.moyu.chuanqirensheng.cloud.loginsdk

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.sub.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.sub.saver.CloudSaverManager
import com.moyu.chuanqirensheng.util.getVersion
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.core.AppWrapper
import com.moyu.core.tapClientId
import com.moyu.core.tapClientToken
import com.taptap.sdk.compliance.TapTapCompliance
import com.taptap.sdk.compliance.TapTapComplianceCallback
import com.taptap.sdk.compliance.constants.ComplianceMessage
import com.taptap.sdk.compliance.option.TapTapComplianceOptions
import com.taptap.sdk.core.TapTapRegion
import com.taptap.sdk.core.TapTapSdk
import com.taptap.sdk.core.TapTapSdkOptions
import com.taptap.sdk.login.Scopes
import com.taptap.sdk.login.TapTapAccount
import com.taptap.sdk.login.TapTapLogin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class GameSdkDefaultProcessor : GameSdkProcessor {
    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var init = false
    private var isLogin = false
    private var isAntiAddict = false

    private var initDone = false
    private var isLoginIng = false

    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }

    // 初始化SDK，判断是否有登录态，没有则调用taptap登录，
    // 登录后校验防沉迷，若返回登录成功，则正常显示菜单，否则弹窗提示错误原因，并退出
    override fun initSDK() {
        if (initDone) return
        initDone = true

        TapTapSdk.init(
            context = GameApp.instance,
            sdkOptions = TapTapSdkOptions(
                clientId = tapClientId,
                clientToken = tapClientToken,
                region = TapTapRegion.CN,
                channel = platformChannel(),
                gameVersion = getVersion(),
                enableLog = DebugManager.debug
            ),
            options = arrayOf(
                TapTapComplianceOptions(
                    showSwitchAccount = false, // 是否允许切换账号
                    useAgeRange = true        // 是否需要真实年龄段
                )
            )
        )

        // 2. 直接检测本地缓存的账户
        TapTapLogin.getCurrentTapAccount()?.let { p ->
            dealAfterLogin(p.name ?: "", p.unionId, p.avatar ?: "")
        } ?: login()           // 未登录则弹出 Tap 登录
    }

    override fun login() {
        try {
            if (isLoginIng) return
            isLoginIng = true

            TapTapLogin.loginWithScopes(
                GameApp.instance.activity,
                arrayOf(Scopes.SCOPE_PUBLIC_PROFILE),
                object :
                    com.taptap.sdk.kit.internal.callback.TapTapCallback<com.taptap.sdk.login.TapTapAccount> {

                    override fun onSuccess(result: TapTapAccount) {
                        isLoginIng = false
                        val p = result
                        dealAfterLogin(p.name ?: "", p.unionId, p.avatar ?: "")
                    }

                    override fun onCancel() {
                        isLoginIng = false
                        ("用户取消登录").toast()
                    }

                    override fun onFail(exception: com.taptap.sdk.kit.internal.exception.TapTapException) {
                        isLoginIng = false
                        ("登录失败：${exception.message}").toast()
                    }
                }
            )
        } catch (e: Exception) {
            isLogin = false
            ("taptap登录异常：" + e.message).toast()
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return antiAddictionStatus
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String {
        return avatar.value ?: "skill_7019"
    }

    override fun getUserName(): String? {
        return userName.value
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun dealAfterLogin(name: String, id: String, avatarUrl: String) {
        loginStatus.value = true
        userName.value = name
        avatar.value = avatarUrl
        objectId.value = id
        checkAntiAddiction()
        AppWrapper.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value ?: "")
            } else if (oldAccount != objectId.value) {
                "不支持账号切换，请卸载重装".toast()
                delay(2000)
                killSelf()
            }
        }
        tryLogin()
    }

    override fun checkAntiAddiction() {
        if (antiAddictionStatus.value || PrivacyManager.antiAddictVerified) {
            return
        }
        if (isAntiAddict) return
        isAntiAddict = true
        try {
            checkCompliance()
            TapTapCompliance.startup(activity = GameApp.instance.activity, userId = GameApp.instance.getObjectId()?: "")
            isAntiAddict = false
        } catch (e: Exception) {
            isAntiAddict = false
            ("防沉迷验证异常：" + e.message).toast()
            AppWrapper.globalScope.launch {
                setBooleanValueByKey(KEY_VERIFIED, true)
            }
            PrivacyManager.antiAddictVerified = true
        }
    }

    override fun isAgeIn8To16(): Boolean {
        return TapTapCompliance.getAgeRange() == 8
    }

    override fun isAgeUnder8(): Boolean {
        return TapTapCompliance.getAgeRange() == 0
    }

    fun isAdult(): Boolean {
        return TapTapCompliance.getAgeRange() in listOf(-1, 16, 18)
    }

    override fun quitGame(onExit: () -> Unit) {
        onExit()
    }

    fun tryLogin() {
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            RetrofitModel.getLoginData()
            withContext(Dispatchers.Main) {
                repo.doInitAfterLogin()
            }
            if (LoginManager.instance.newUser) { // 新用户
                // 看下是不是有存档
                CloudSaverManager.checkIfNewUserHaveCloudSave()
            }
        }
    }

    private fun checkCompliance() {
        TapTapCompliance.registerComplianceCallback(
            callback = object : TapTapComplianceCallback {
                override fun onComplianceResult(code: Int, extra: Map<String, Any>?) {
                    when (code) {
                        ComplianceMessage.LOGIN_SUCCESS -> {
                            antiAddictionStatus.value = true
                            antiAddictionContent.value = "已通过防沉迷认证"
                            setBooleanValueByKey(KEY_VERIFIED, true)
                            // 需要的话：开始倒计时提示
                            var remainingTimeInSeconds = TapTapCompliance.getRemainingTime()
//                            "要删除：年龄枚举=${TapTapCompliance.getAgeRange()}，剩游戏时间：${remainingTimeInSeconds / 60}分钟".toast()
                            if (remainingTimeInSeconds > 0 && !isAdult()) {
                                "剩余游戏时间：${remainingTimeInSeconds / 60}分钟".toast()
                                AppWrapper.globalScope.launch {
                                    while (true) {
                                        if (remainingTimeInSeconds <= 300) {
                                            "游戏剩余时间即将结束，请尽快保存游戏".toast()
                                            delay(remainingTimeInSeconds * 1000L)
                                            killSelf()
                                            break
                                        } else {
                                            delay(60 * 5000L)
                                            remainingTimeInSeconds -= 300
                                            "剩余游戏时间：${remainingTimeInSeconds / 60}分钟".toast()
                                        }
                                    }
                                }
                            }
                        }

                        ComplianceMessage.PERIOD_RESTRICT,
                        ComplianceMessage.DURATION_LIMIT -> {
                            antiAddictionStatus.value = false
                            antiAddictionContent.value = "当前时间段无法进行游戏"
                            "未成年玩家当前时间段无法进行游戏，请稍后再试".toast()
                            AppWrapper.globalScope.launch(Dispatchers.Main) {
                                delay(5000)
                                quitGame { killSelf() }
                            }
                        }

                        ComplianceMessage.EXITED -> {          // 退出登录
                            antiAddictionStatus.value = false
                            quitGame { killSelf() }
                        }

                        ComplianceMessage.SWITCH_ACCOUNT -> {  // 用户点了切换账号
                            antiAddictionStatus.value = false
                            // 回到登录页
                        }
                    }
                }
            }
        )
    }
}