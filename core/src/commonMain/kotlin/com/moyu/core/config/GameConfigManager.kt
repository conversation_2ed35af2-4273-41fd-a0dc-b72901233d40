package com.moyu.core.config

import com.moyu.core.exception.configError
import com.moyu.core.model.Ally
import com.moyu.core.model.AllyLevel
import com.moyu.core.model.AllyStar
import com.moyu.core.model.Arena
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.Common
import com.moyu.core.model.DayReward
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Dungeon
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.Gift
import com.moyu.core.model.Lucky
import com.moyu.core.model.Pool
import com.moyu.core.model.Pvp
import com.moyu.core.model.PvpRank
import com.moyu.core.model.Quest
import com.moyu.core.model.Sell
import com.moyu.core.model.Sign
import com.moyu.core.model.Story
import com.moyu.core.model.Talent
import com.moyu.core.model.Tcg
import com.moyu.core.model.Title
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Unlock
import com.moyu.core.model.Vip
import com.moyu.core.model.allyIdToMainId
import com.moyu.core.model.equipIdToMainId
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.scrollIdToMainId
import com.moyu.core.model.skill.skillIdToLevel
import kotlin.math.min

class GameConfigManager : ConfigHolder {
    private val configLoaders = hashMapOf<String, List<Any>>()
    private var poolHash = mapOf<Int, Pool>()
    private var allyHash = mapOf<Int, List<Ally>>() // 相同mainId组成的list
    private var equipHash = mapOf<Int, List<Equipment>>() // 相同mainId组成的list
    private var scrollHash = mapOf<Int, List<Scroll>>() // 相同mainId组成的list
    private var skillHash = mapOf<Int, List<Skill>>() // 相同level组成的list

    private inline fun <reified T> getPoolByKey(key: String): List<T> {
        return configLoaders[key]!! as List<T>
    }

    override fun setGameConfig(key: String, pool: List<Any>) {
        when (key) {
            POOL_FILE_NAME  -> poolHash = (pool as List<Pool>).associateBy { it.id }
            ALLY_FILE_NAME  -> allyHash = (pool as List<Ally>).groupBy { it.mainId }
            EQUIPMENT_FILE_NAME  -> equipHash = (pool as List<Equipment>).groupBy { it.mainId }
            SCROLL_FILE_NAME  -> scrollHash = (pool as List<Scroll>).groupBy { it.mainId }
            SKILL_FILE_NAME  -> skillHash = (pool as List<Skill>).groupBy { it.level }
            else            -> configLoaders[key] = pool
        }
    }

    override fun getSkillPool(): List<Skill> {
        return skillHash.values.flatten()
    }

    override fun getSkillPool(level: Int): List<Skill> {
        return skillHash[level]?: configError(level)
    }

    override fun getBuffPool(): List<Buff> {
        return getPoolByKey(BUFF_FILE_NAME)
    }

    override fun getAllyPool(): List<Ally> {
        return allyHash.values.flatten()
    }

    override fun getAllyPool(mainId: Int): List<Ally> {
        return allyHash[mainId]?: emptyList()
    }

    override fun getEventPool(): List<Event> {
        return getPoolByKey(EVENT_FILE_NAME)
    }

    override fun getTalentPool(type: Int): List<Talent> {
        return when (type) {
            1 -> getTalent1Pool()
            2 -> getTalent2Pool()
            3 -> getTalent3Pool()
            else -> emptyList()
        }
    }

    override fun getTalent1Pool(): List<Talent> {
        return getPoolByKey(TALENT1_FILE_NAME)
    }

    override fun getTalent2Pool(): List<Talent> {
        return getPoolByKey(TALENT2_FILE_NAME)
    }

    override fun getTalent3Pool(): List<Talent> {
        return getPoolByKey(TALENT3_FILE_NAME)
    }

    override fun getSellPool(): List<Sell> {
        return getPoolByKey(SELL_FILE_NAME)
    }

    override fun getGameTaskPool(): List<Quest> {
        return getPoolByKey(TASK_FILE_NAME)
    }

    override fun getUnlockPool(): List<Unlock> {
        return getPoolByKey(UNLOCK_FILE_NAME)
    }

    override fun getScrollPool(mainId: Int): List<Scroll> {
        return scrollHash[mainId]?: configError(mainId)
    }

    override fun getVipPool(): List<Vip> {
        return getPoolByKey(VIP_FILE_NAME)
    }

    override fun getCombinedBuffPool(): List<Buff> {
        return getPoolByKey(COMBINEDBUFF_FILE_NAME)
    }

    override fun getBattlePass1Pool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS1_FILE_NAME)
    }

    override fun getBattlePass2Pool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS2_FILE_NAME)
    }

    override fun getBattlePass3Pool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS3_FILE_NAME)
    }

    override fun getBattlePass4Pool(): List<BattlePass> {
        return getPoolByKey(WAR_PASS4_FILE_NAME)
    }

    override fun getSignPool(): List<Sign> {
        return getPoolByKey(SIGN_FILE_NAME)
    }

    override fun getDungeonPool(): List<Dungeon> {
        return getPoolByKey(DUNGEON_FILE_NAME)
    }

    override fun getPvpPool(type: Int): List<Pvp> {
        return getPoolByKey<Pvp>(PVP_FILE_NAME).filter { it.type == type }
    }

    override fun getDrawPool(): List<DrawItem> {
        return getPoolByKey(DRAW_FILE_NAME)
    }

    override fun getGiftPool(): List<Gift> {
        return getPoolByKey(GIFT_FILE_NAME)
    }

    override fun getDayRewardPool(): List<DayReward> {
        return getPoolByKey(DAY_REWARD_FILE_NAME)
    }

    override fun getStoryPool(): List<Story> {
        return getPoolByKey(STORY_FILE_NAME)
    }

    override fun getTurnTablePool(): List<TurnTable> {
        return getPoolByKey(TURNTABLE_FILE_NAME)
    }

    override fun getLuckyPool(): List<Lucky> {
        return getPoolByKey(LUCKY_FILE_NAME)
    }

    override fun getTitlePool(): List<Title> {
        return getPoolByKey(POSITION_FILE_NAME)
    }

    override fun getEquipPool(): List<Equipment> {
        return equipHash.values.flatten()
    }

    override fun getEquipPool(mainId: Int): List<Equipment> {
        return equipHash[mainId]?: configError(mainId)
    }

    override fun getArenaPool(): List<Arena> {
        return getPoolByKey(ARENA_FILE_NAME)
    }

    override fun getTowerPool(): List<Tower> {
        return getPoolByKey(TOWER_FILE_NAME)
    }

    override fun getReputationLevelPool(): List<ReputationLevel> {
        return getPoolByKey(REPUTATION_LEVEL_FILE_NAME)
    }

    override fun getAllyLevelPool(): List<AllyLevel> {
        return getPoolByKey(ALLY_LEVEL_FILE_NAME)
    }

    override fun getAllyStarPool(): List<AllyStar> {
        return getPoolByKey(ALLY_STAR_FILE_NAME)
    }

    override fun getPvpRankPool(): List<PvpRank> {
        return getPoolByKey(PVP_RANK_FILE_NAME)
    }

    override fun getTcgPool(): List<Tcg> {
        return getPoolByKey(TCG_FILE_NAME)
    }

    override fun getSkillById(skillId: Int): Skill {
        val level = skillId.skillIdToLevel()
        return getSkillPool(level).find { it.id == skillId } ?: configError(skillId)
    }

    override fun getSkillByIdNullable(skillId: Int): Skill? {
        val level = skillId.skillIdToLevel()
        return getSkillPool(level).firstOrNull { it.id == skillId }
    }

    override fun getEquipById(equipId: Int): Equipment {
        return getEquipPool(equipId.equipIdToMainId()).find { it.id == equipId } ?: configError(equipId)
    }

    override fun getGameTaskById(id: Int): Quest {
        return getGameTaskPool().find { it.id == id } ?: configError(id)
    }

    /**
     * todo ally都只配置了star1的数据，需要修改
     * id，skill，star
     */
    override fun getAllyById(id: Int): Ally {
        val mainId = id.allyIdToMainId()
        val star = id % 100
        val star1Id = id - star + 1
        val star1Ally = getAllyPool(mainId).find { it.id == star1Id } ?: configError(id)
        val starAlly = star1Ally.copy(id = id, star = star)
        return starAlly
    }

    override fun getAlly(mainId: Int, level: Int, star: Int): Ally {
        val star1Level1Id = mainId * 100000 + 101
        val star1LevelId = star1Level1Id + (level - 1) * 100
        val starLevelId = star1LevelId + star - 1
        return getAllyPool(mainId).first { it.id == star1LevelId }.copy(id = starLevelId, star = star)
    }

    override fun getAllyNextLevel(id: Int): Ally {
        return getAllyById(id + 100)
    }

    override fun getAllyNextStar(id: Int): Ally {
        return getAllyById(id + 1)
    }

    override fun getScrollById(id: Int): Scroll {
        return getScrollPool(id.scrollIdToMainId()).find { it.id == id } ?: configError(id)
    }

    override fun getBuffById(buffId: Int): Buff {
        return getBuffPool().find { it.id == buffId } ?: configError(buffId)
    }

    override fun getUnlockById(id: Int): Unlock {
        return getUnlockPool().find { it.id == id } ?: configError(id)
    }

    override fun getPoolById(id: Int): Pool {
        return poolHash[id] ?: configError(id)
    }

    override fun getConstA(): Double {
        return 500.0
    }

    override fun getConstB(): Double {
        return 500.0
    }

    override fun getFirstAwardPoolId(): Int {
        return getValueById(84).toInt()
    }

    override fun getFirstEndingAwardPoolId(): Int {
        return getValueById(69).toInt()
    }

    override fun getInitGold(): Int {
        return getValueById(54).toInt()
    }

    override fun getInitStone(): Int {
        return getValueById(81).toInt()
    }

    override fun getInitWood(): Int {
        return getValueById(76).toInt()
    }

    override fun getEndingAwardLevel(age: Int): Int {
        val requiredAge = getValueById(8).split(",").map { it.toInt() }
        return requiredAge.indexOfLast { it <= age }
    }

    override fun getEndingDiamondLevel(age: Int): Int {
        val requiredAge = getValueById(9).split(",").map { it.toInt() }
        return requiredAge.indexOfLast { it <= age }
    }

    override fun getEndingAwardDiamond(difficult: Int, level: Int): Int {
        val diamondList = getValueById(101 + difficult).split(",").map { it.toInt() }
        return diamondList[level]
    }

    override fun getQuality1AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1001 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality2AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1011 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality3AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1021 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality4AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1031 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality5AllyNum(difficult: Int, level: Int): Int {
        return getValueById(1041 + difficult).split(",").map { it.toInt() }[level]
    }

    override fun getQuality1AllyPool(): List<Int> {
        return getValueById(1101).split(",").map { it.toInt() }
    }

    override fun getQuality2AllyPool(): List<Int> {
        return getValueById(1102).split(",").map { it.toInt() }
    }

    override fun getQuality3AllyPool(): List<Int> {
        return getValueById(1103).split(",").map { it.toInt() }
    }

    override fun getQuality4AllyPool(): List<Int> {
        return getValueById(1104).split(",").map { it.toInt() }
    }

    override fun getQuality5AllyPool(): List<Int> {
        return getValueById(1105).split(",").map { it.toInt() }
    }

    override fun getRefreshShopCost(): Int {
        return getValueById(3).toInt()
    }

    override fun getKeyToResource1Rate(): Int {
        return getValueById(4).toInt()
    }

    override fun getDailyShopRefreshCount(): Int {
        return getValueById(2).toInt()
    }

    override fun getWarPassQuestCount(): Int {
        return getValueById(6).toInt()
    }

    override fun getNewQuestCount(): Int {
        return getValueById(7).toInt()
    }

    override fun getDailyQuestCount(): Int {
        return getValueById(1).toInt()
    }

    override fun getMaxOtherUseYourCount(): Int {
        return getValueById(52).toInt()
    }

    override fun getMaxUseOtherCount(): Int {
        return getValueById(53).toInt()
    }

    override fun getShareCodeAwardKeyNum(): Int {
        return getValueById(78).toInt()
    }

    override fun getMaxOneDayDiamondLimit(): Int {
        // todo 防止被简单篡改配置，内存加一个数
        return min(1500, getValueById(66).toInt())
    }

    override fun getImageShareAwardNum(): Int {
        return getValueById(72).toInt()
    }

    override fun getTextShareAwardNum(): Int {
        return getValueById(71).toInt()
    }

    override fun getShopDataByIndex(index: Int): List<Int> {
        return getValueById(55 + index).split(",").map { it.toInt() }
    }

    override fun getUnlockTalentPageLevel(): Int {
        return getValueById(77).toInt()
    }

    override fun getFamousDiamond(): Int {
        return getValueById(80).toInt()
    }

    override fun getAllyCouponRate(): Int {
        return getValueById(82).toInt()
    }

    override fun getHeroCouponRate(): Int {
        return getValueById(83).toInt()
    }

    override fun getActivityCouponRate(): Int {
        return getValueById(90).toInt()
    }

    override fun getInitCouponPoolId(): Int {
        return getValueById(84).toInt()
    }

    override fun getCheapLotteryCosts(): List<Int> {
        return getValueById(2101).split(",").map { it.toInt() }
    }

    override fun getExpensiveLotteryCosts(): List<Int> {
        return getValueById(2102).split(",").map { it.toInt() }
    }

    override fun getHolidayLotteryCosts(): List<Int> {
        return getValueById(2103).split(",").map { it.toInt() }
    }

    override fun getTowerAwardKey(towerLevel: Int): Int {
        val index = if (towerLevel <= 500) 0 else if (towerLevel <= 1000) 1 else 2
        return getValueById(86).split(",").map { it.toInt() }[index]
    }

    override fun getReputationQuestCount(): Int {
        return getValueById(87).toInt()
    }

    override fun getMaxPower(): Int {
        return getValueById(2201).toInt()
    }

    override fun getEachStageConsumePower(): Int {
        return getValueById(2202).toInt()
    }

    override fun getEachEndlessConsumePower(): Int {
        return getValueById(2203).toInt()
    }

    override fun getEachRecoverPower(): Int {
        return getValueById(2204).toInt()
    }

    override fun getLuckyWeights(): List<Int> {
        return getValueById(2001).split(",").map { it.toInt() }
    }

    override fun getLuckyOutputs(): List<Int> {
        return getValueById(2002).split(",").map { it.toInt() }
    }

    override fun getGiftById(value: Int): Gift? {
        return getGiftPool().find { it.id == value }
    }

    private fun getValueById(id: Int) = getPoolByKey<Common>(COMMON_FILE_NAME).first { it.id == id }.num
}